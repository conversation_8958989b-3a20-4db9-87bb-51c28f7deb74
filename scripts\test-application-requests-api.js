const { chromium } = require('playwright');
const readline = require('readline');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Load test data
const testData = JSON.parse(fs.readFileSync(path.join(__dirname, '../src/tests-data/application-requests-api-data.json'), 'utf8'));

// API endpoints - Use the same base URL as in playwright.config.ts
const BASE_URL = process.env.API_URL || 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';
const ENDPOINTS = {
    REGISTER: '/api/auth/register',
    VERIFY_EMAIL: '/api/auth/verify-email',
    LOGIN: '/api/auth/login',
    APPLICATION_REQUESTS: '/api/application-requests',
    APPLICATION_REQUESTS_CURRENT_USER: '/api/application-requests/current-user',
    APPLICATION_REQUESTS_BY_ID: (id) => `/api/application-requests/${id}`,
    APPLICATION_REQUESTS_REQUEST_UPDATE: (id) => `/api/application-requests/${id}/request-update`,
    APPLICATION_REQUESTS_APPROVE: (id) => `/api/application-requests/${id}/approve`,
    APPLICATION_REQUESTS_REJECT: (id) => `/api/application-requests/${id}/reject`
};

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to prompt user input
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

// Helper function to make API requests
async function makeRequest(context, method, endpoint, data = null, isMultipart = false) {
    const options = {
        headers: {
            'Accept': '*/*'
        }
    };

    if (data) {
        if (isMultipart) {
            options.multipart = data;
        } else {
            options.data = data;
            options.headers['Content-Type'] = 'application/json';
        }
    }

    const response = await context[method.toLowerCase()](BASE_URL + endpoint, options);
    return response;
}

// Step 1: Register mentor account
async function registerMentor(context, email, password) {
    console.log('\n=== STEP 1: REGISTERING MENTOR ACCOUNT ===');
    
    const registrationData = {
        Email: email,
        Password: password,
        FullName: testData.mentorRegistration.fullName,
        Role: testData.mentorRegistration.role.toString(),
        Bio: testData.mentorRegistration.bio,
        Expertises: testData.mentorRegistration.expertises,
        ProfessionalSkill: testData.mentorRegistration.professionalSkill,
        Experience: testData.mentorRegistration.experience,
        CommunicationPreference: testData.mentorRegistration.communicationPreference.toString(),
        Goals: testData.mentorRegistration.goals,
        CourseCategoryIds: testData.mentorRegistration.courseCategoryIds,
        SessionFrequency: testData.mentorRegistration.sessionFrequency.toString(),
        Duration: testData.mentorRegistration.duration.toString(),
        LearningStyle: testData.mentorRegistration.learningStyle.toString(),
        TeachingStyles: testData.mentorRegistration.teachingStyles.map(x => x.toString())
    };

    try {
        const response = await makeRequest(context, 'POST', ENDPOINTS.REGISTER, registrationData, true);
        const responseBody = await response.json();
        
        console.log(`Registration response status: ${response.status()}`);
        console.log('Registration response:', JSON.stringify(responseBody, null, 2));
        
        if (response.status() === 200) {
            console.log('✅ Registration successful! Check your email for verification code.');
            return true;
        } else {
            console.log('❌ Registration failed:', responseBody);
            return false;
        }
    } catch (error) {
        console.error('❌ Registration error:', error);
        return false;
    }
}

// Step 2: Verify email with code
async function verifyEmail(context, email, code) {
    console.log('\n=== STEP 2: VERIFYING EMAIL ===');
    
    const verificationData = {
        email: email,
        code: code
    };

    try {
        const response = await makeRequest(context, 'POST', ENDPOINTS.VERIFY_EMAIL, verificationData);
        const responseBody = await response.json();
        
        console.log(`Verification response status: ${response.status()}`);
        console.log('Verification response:', JSON.stringify(responseBody, null, 2));
        
        if (response.status() === 200) {
            console.log('✅ Email verification successful!');
            return responseBody.accessToken;
        } else {
            console.log('❌ Email verification failed:', responseBody);
            return null;
        }
    } catch (error) {
        console.error('❌ Verification error:', error);
        return null;
    }
}

// Step 3: Login to get access token (alternative if verify email doesn't return token)
async function login(context, email, password) {
    console.log('\n=== STEP 3: LOGGING IN ===');

    const loginData = {
        email: email,
        password: password
    };

    try {
        const response = await makeRequest(context, 'POST', ENDPOINTS.LOGIN, loginData);
        const responseBody = await response.json();

        console.log(`Login response status: ${response.status()}`);
        console.log('Login response:', JSON.stringify(responseBody, null, 2));

        if (response.status() === 200) {
            console.log('✅ Login successful!');
            return responseBody.data.accessToken;
        } else {
            console.log('❌ Login failed:', responseBody);
            return null;
        }
    } catch (error) {
        console.error('❌ Login error:', error);
        return null;
    }
}

// Test API functions with authentication
async function testCreateApplicationRequest(context, accessToken) {
    console.log('\n=== TESTING CREATE APPLICATION REQUEST ===');

    const requestData = {
        Education: testData.createRequest.Education,
        WorkExperience: testData.createRequest.WorkExperience,
        Certifications: testData.createRequest.Certifications,
        Description: testData.createRequest.Description
    };

    try {
        const response = await context.post(BASE_URL + ENDPOINTS.APPLICATION_REQUESTS, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Accept': '*/*'
            },
            multipart: requestData
        });

        const responseBody = await response.json();

        console.log(`Create request response status: ${response.status()}`);
        console.log('Create request response:', JSON.stringify(responseBody, null, 2));

        if (response.status() === 200) {
            console.log('✅ Create application request successful!');
            return responseBody.data?.id || responseBody.id;
        } else {
            console.log('❌ Create application request failed:', responseBody);
            return null;
        }
    } catch (error) {
        console.error('❌ Create application request error:', error);
        return null;
    }
}

async function testGetCurrentUserApplicationRequest(context, accessToken) {
    console.log('\n=== TESTING GET CURRENT USER APPLICATION REQUEST ===');

    try {
        const response = await context.get(BASE_URL + ENDPOINTS.APPLICATION_REQUESTS_CURRENT_USER, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Accept': '*/*'
            }
        });

        const responseBody = await response.json();

        console.log(`Get current user request response status: ${response.status()}`);
        console.log('Get current user request response:', JSON.stringify(responseBody, null, 2));

        if (response.status() === 200) {
            console.log('✅ Get current user application request successful!');
            return responseBody.data;
        } else {
            console.log('❌ Get current user application request failed:', responseBody);
            return null;
        }
    } catch (error) {
        console.error('❌ Get current user application request error:', error);
        return null;
    }
}

async function testUpdateApplicationRequest(context, accessToken, requestId) {
    console.log('\n=== TESTING UPDATE APPLICATION REQUEST ===');

    const updateData = {
        Id: requestId,
        Education: testData.updateRequest.Education,
        WorkExperience: testData.updateRequest.WorkExperience,
        Certifications: testData.updateRequest.Certifications,
        Description: testData.updateRequest.Description
    };

    try {
        const response = await context.put(BASE_URL + ENDPOINTS.APPLICATION_REQUESTS, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Accept': '*/*'
            },
            multipart: updateData
        });

        const responseBody = await response.json();

        console.log(`Update request response status: ${response.status()}`);
        console.log('Update request response:', JSON.stringify(responseBody, null, 2));

        if (response.status() === 200) {
            console.log('✅ Update application request successful!');
            return true;
        } else {
            console.log('❌ Update application request failed:', responseBody);
            return false;
        }
    } catch (error) {
        console.error('❌ Update application request error:', error);
        return false;
    }
}

async function testGetApplicationRequestById(context, accessToken, requestId) {
    console.log('\n=== TESTING GET APPLICATION REQUEST BY ID ===');

    try {
        const response = await context.get(BASE_URL + ENDPOINTS.APPLICATION_REQUESTS_BY_ID(requestId), {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Accept': '*/*'
            }
        });

        const responseBody = await response.json();

        console.log(`Get request by ID response status: ${response.status()}`);
        console.log('Get request by ID response:', JSON.stringify(responseBody, null, 2));

        if (response.status() === 200) {
            console.log('✅ Get application request by ID successful!');
            return responseBody.data;
        } else {
            console.log('❌ Get application request by ID failed:', responseBody);
            return null;
        }
    } catch (error) {
        console.error('❌ Get application request by ID error:', error);
        return null;
    }
}

// Main function to run the complete test flow
async function main() {
    console.log('🚀 MENTOR APPLICATION REQUESTS API TESTING SCRIPT');
    console.log('================================================');
    console.log('This script will test the mentor application requests API with 3-step registration:');
    console.log('1. Register mentor account with real email');
    console.log('2. Verify email with code sent to your email');
    console.log('3. Test application requests API endpoints');
    console.log('');

    const browser = await chromium.launch();
    const context = await browser.newContext();

    try {
        // Step 1: Get user input for registration
        console.log('📧 Please enter a real email from 10min email service:');
        const email = await askQuestion('Email: ');

        console.log('🔐 Please enter a password for the account:');
        const password = await askQuestion('Password: ');

        // Step 2: Register mentor account
        const registrationSuccess = await registerMentor(context, email, password);
        if (!registrationSuccess) {
            console.log('❌ Registration failed. Exiting...');
            return;
        }

        // Step 3: Get verification code from user
        console.log('\n📬 Please check your email and enter the 6-digit verification code:');
        const verificationCode = await askQuestion('Verification Code: ');

        // Step 4: Verify email
        let accessToken = await verifyEmail(context, email, verificationCode);

        // If verification doesn't return token, try login
        if (!accessToken) {
            console.log('🔄 Verification completed but no token returned. Trying login...');
            accessToken = await login(context, email, password);
        }

        if (!accessToken) {
            console.log('❌ Failed to get access token. Exiting...');
            return;
        }

        console.log('🎉 Authentication successful! Starting API tests...');

        // Step 5: Test Application Requests APIs
        let requestId = null;

        // Test 1: Create application request
        requestId = await testCreateApplicationRequest(context, accessToken);

        // Test 2: Get current user application request
        await testGetCurrentUserApplicationRequest(context, accessToken);

        // Test 3: Update application request (if we have a request ID)
        if (requestId) {
            await testUpdateApplicationRequest(context, accessToken, requestId);

            // Test 4: Get application request by ID
            await testGetApplicationRequestById(context, accessToken, requestId);
        }

        console.log('\n🎊 All API tests completed!');
        console.log('Check the responses above to verify the API functionality.');

    } catch (error) {
        console.error('❌ Script error:', error);
    } finally {
        await browser.close();
        rl.close();
    }
}

// Run the script
if (require.main === module) {
    main().catch(console.error);
}
