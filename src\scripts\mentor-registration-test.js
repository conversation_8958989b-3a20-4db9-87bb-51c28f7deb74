const readline = require('readline');
const https = require('https');
const FormData = require('form-data');

// Configuration
const BASE_URL = 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';
const ENDPOINTS = {
    REGISTER: '/api/auth/register',
    VERIFY_EMAIL: '/api/auth/verify-email',
    LOGIN: '/api/auth/login',
    APPLICATION_REQUESTS: '/api/application-requests'
};

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to make HTTP requests
function makeRequest(method, endpoint, data = null, headers = {}, isFormData = false) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        
        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Accept': 'application/json',
                ...headers
            }
        };

        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = responseData ? JSON.parse(responseData) : {};
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: parsedData,
                        rawData: responseData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: null,
                        rawData: responseData
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            if (isFormData) {
                // For multipart form data
                data.pipe(req);
            } else {
                // For JSON data
                req.write(JSON.stringify(data));
            }
        } else {
            req.end();
        }
    });
}

// Helper function to get user input
function getUserInput(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Step 1: Register mentor account
async function registerMentor(email, password, fullName) {
    console.log('\n=== STEP 1: REGISTERING MENTOR ACCOUNT ===');
    
    const formData = new FormData();
    formData.append('Email', email);
    formData.append('Password', password);
    formData.append('FullName', fullName);
    formData.append('Role', '1'); // 1 = Mentor role
    formData.append('Bio', 'Test mentor for API testing');
    formData.append('Expertises', '8a5bc300-21c4-47d0-bb33-27d0a709d417');
    formData.append('ProfessionalSkill', 'Software Development');
    formData.append('Experience', '5 years of experience in software development');
    formData.append('CommunicationPreference', '1');
    formData.append('Goals', 'Help others learn programming and grow their skills');
    formData.append('CourseCategoryIds', 'f47ac10b-58cc-4372-a567-0e02b2c3d479');
    formData.append('SessionFrequency', '2');
    formData.append('Duration', '60');
    formData.append('LearningStyle', '1');
    formData.append('TeachingStyles', '1');

    try {
        const response = await makeRequest('POST', ENDPOINTS.REGISTER, formData, {
            'Accept': '*/*',
            ...formData.getHeaders()
        }, true);

        console.log(`Registration Response Status: ${response.status}`);
        console.log('Registration Response:', response.rawData);

        if (response.status === 200) {
            console.log('✅ Registration successful! Check your email for verification code.');
            return true;
        } else {
            console.log('❌ Registration failed:', response.rawData);
            return false;
        }
    } catch (error) {
        console.error('❌ Registration error:', error.message);
        return false;
    }
}

// Step 2: Verify email with code
async function verifyEmail(email, code) {
    console.log('\n=== STEP 2: VERIFYING EMAIL ===');
    
    const data = {
        email: email,
        code: code
    };

    try {
        const response = await makeRequest('POST', ENDPOINTS.VERIFY_EMAIL, data, {
            'Content-Type': 'application/json'
        });

        console.log(`Verification Response Status: ${response.status}`);
        console.log('Verification Response:', response.rawData);

        if (response.status === 200 && response.data && response.data.accessToken) {
            console.log('✅ Email verification successful!');
            console.log('Access Token:', response.data.accessToken);
            return {
                success: true,
                accessToken: response.data.accessToken,
                refreshToken: response.data.refreshToken
            };
        } else {
            console.log('❌ Email verification failed:', response.rawData);
            return { success: false };
        }
    } catch (error) {
        console.error('❌ Email verification error:', error.message);
        return { success: false };
    }
}

// Step 3: Test application requests API
async function testApplicationRequestsAPI(accessToken) {
    console.log('\n=== STEP 3: TESTING APPLICATION REQUESTS API ===');
    
    // Test 1: Create application request
    console.log('\n--- Testing CREATE Application Request ---');
    
    const createData = new FormData();
    createData.append('Education', 'Master\'s in Computer Science from Stanford University');
    createData.append('WorkExperience', '5 years as Senior Software Developer at Google, leading teams of 8+ developers');
    createData.append('Certifications[0]', 'AWS Certified Solutions Architect');
    createData.append('Certifications[1]', 'Scrum Master Certified');
    createData.append('Certifications[2]', 'Google Cloud Professional');
    createData.append('Description', 'Experienced developer passionate about mentoring junior developers and sharing knowledge in full-stack development');

    try {
        const createResponse = await makeRequest('POST', ENDPOINTS.APPLICATION_REQUESTS, createData, {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': '*/*',
            ...createData.getHeaders()
        }, true);

        console.log(`Create Application Response Status: ${createResponse.status}`);
        console.log('Create Application Response:', createResponse.rawData);

        if (createResponse.status === 200) {
            console.log('✅ Application request created successfully!');
            
            // Test 2: Get current user application request
            console.log('\n--- Testing GET Current User Application Request ---');
            
            const getCurrentResponse = await makeRequest('GET', '/api/application-requests/current-user', null, {
                'Authorization': `Bearer ${accessToken}`
            });

            console.log(`Get Current Application Response Status: ${getCurrentResponse.status}`);
            console.log('Get Current Application Response:', getCurrentResponse.rawData);

            if (getCurrentResponse.status === 200) {
                console.log('✅ Successfully retrieved current user application request!');
                return true;
            } else {
                console.log('❌ Failed to retrieve current user application request');
                return false;
            }
        } else {
            console.log('❌ Failed to create application request:', createResponse.rawData);
            return false;
        }
    } catch (error) {
        console.error('❌ Application requests API test error:', error.message);
        return false;
    }
}

// Main function
async function main() {
    console.log('🚀 MENTOR REGISTRATION AND APPLICATION REQUESTS API TEST');
    console.log('=====================================================');
    console.log('This script will test the complete mentor registration flow:');
    console.log('1. Register a new mentor account');
    console.log('2. Verify email with code sent to your email');
    console.log('3. Test application requests API');
    console.log('');
    console.log('Please use a real email address (e.g., from 10minutemail.com)');
    console.log('');

    try {
        // Get user input
        const email = await getUserInput('Enter your email address: ');
        const password = await getUserInput('Enter your password: ');
        const fullName = await getUserInput('Enter your full name: ');

        // Step 1: Register
        const registrationSuccess = await registerMentor(email, password, fullName);
        if (!registrationSuccess) {
            console.log('❌ Registration failed. Exiting...');
            rl.close();
            return;
        }

        // Step 2: Verify email
        const verificationCode = await getUserInput('\nEnter the verification code sent to your email: ');
        const verificationResult = await verifyEmail(email, verificationCode);
        
        if (!verificationResult.success) {
            console.log('❌ Email verification failed. Exiting...');
            rl.close();
            return;
        }

        // Step 3: Test application requests API
        const apiTestSuccess = await testApplicationRequestsAPI(verificationResult.accessToken);
        
        if (apiTestSuccess) {
            console.log('\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!');
            console.log('✅ Mentor registration: SUCCESS');
            console.log('✅ Email verification: SUCCESS');
            console.log('✅ Application requests API: SUCCESS');
        } else {
            console.log('\n⚠️ Some tests failed. Check the logs above for details.');
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error.message);
    } finally {
        rl.close();
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    registerMentor,
    verifyEmail,
    testApplicationRequestsAPI
};
