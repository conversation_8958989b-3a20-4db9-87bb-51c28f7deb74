{"mentorRegistration": {"fullName": "Test Mentor API", "role": 1, "bio": "Experienced software developer passionate about mentoring", "expertises": ["8a5bc300-21c4-47d0-bb33-27d0a709d417"], "professionalSkill": "Full Stack Development", "experience": "5+ years in software development", "communicationPreference": 1, "goals": "Help junior developers grow their skills", "courseCategoryIds": ["f47ac10b-58cc-4372-a567-0e02b2c3d479"], "sessionFrequency": 2, "duration": 60, "learningStyle": 1, "teachingStyles": [1, 2]}, "createRequest": {"Education": "Master of Computer Science from Stanford University", "WorkExperience": "5 years as Senior Software Engineer at Google", "Certifications": ["AWS Certified Solutions Architect", "Google Cloud Professional"], "Description": "Passionate about mentoring junior developers and sharing knowledge in software engineering best practices"}, "updateRequest": {"Education": "PhD in Computer Science from MIT", "WorkExperience": "8 years as Principal Software Engineer at Microsoft", "Certifications": ["Azure Solutions Architect Expert", "Kubernetes Certified Administrator"], "Description": "Updated: Experienced in leading development teams and implementing scalable cloud solutions"}, "getRequestsParams": {"PageSize": 10, "PageNumber": 1, "Search": "mentor", "ApplicationRequestStatuses": [0, 1, 2]}, "requestUpdate": {"note": "Please update your work experience section with more details about your recent projects"}, "rejectRequest": {"note": "Application rejected due to insufficient experience in the required technology stack"}}