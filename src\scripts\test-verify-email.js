const readline = require('readline');
const https = require('https');

// Configuration
const BASE_URL = 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to make HTTP requests with detailed logging
function makeRequest(method, endpoint, data = null, headers = {}, timeoutMs = 60000) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        
        console.log(`\n--- REQUEST DETAILS ---`);
        console.log(`Method: ${method}`);
        console.log(`URL: ${url.toString()}`);
        console.log(`Headers:`, headers);
        console.log(`Data:`, data);
        console.log(`Timeout: ${timeoutMs}ms`);
        
        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname + url.search,
            method: method,
            timeout: timeoutMs,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Node.js Test Script',
                ...headers
            }
        };

        const startTime = Date.now();
        
        const req = https.request(options, (res) => {
            const responseTime = Date.now() - startTime;
            console.log(`\n--- RESPONSE DETAILS ---`);
            console.log(`Status: ${res.statusCode}`);
            console.log(`Response Time: ${responseTime}ms`);
            console.log(`Response Headers:`, res.headers);
            
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                console.log(`Response Body:`, responseData);
                
                try {
                    const parsedData = responseData ? JSON.parse(responseData) : {};
                    resolve({
                        status: res.statusCode,
                        data: parsedData,
                        rawData: responseData,
                        responseTime: responseTime
                    });
                } catch (error) {
                    console.log('Failed to parse JSON response');
                    resolve({
                        status: res.statusCode,
                        data: null,
                        rawData: responseData,
                        responseTime: responseTime
                    });
                }
            });
        });

        req.on('error', (error) => {
            const responseTime = Date.now() - startTime;
            console.log(`\n--- ERROR DETAILS ---`);
            console.log(`Error after ${responseTime}ms:`, error.message);
            reject(error);
        });

        req.on('timeout', () => {
            const responseTime = Date.now() - startTime;
            console.log(`\n--- TIMEOUT DETAILS ---`);
            console.log(`Request timed out after ${responseTime}ms`);
            req.destroy();
            reject(new Error(`Request timeout after ${timeoutMs}ms`));
        });

        if (data) {
            const jsonData = JSON.stringify(data);
            console.log(`Sending data: ${jsonData}`);
            req.write(jsonData);
        }
        
        req.end();
    });
}

// Get user input
function getUserInput(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Test verify email API
async function testVerifyEmail() {
    console.log('🧪 TEST VERIFY EMAIL API');
    console.log('========================');
    console.log('This script will test the verify email API with detailed logging');
    console.log('');

    try {
        // Get user input
        const email = await getUserInput('Enter email address: ');
        const code = await getUserInput('Enter verification code: ');

        console.log('\n=== TESTING VERIFY EMAIL API ===');
        
        const data = {
            email: email,
            code: code
        };

        // Test with different timeout values
        const timeouts = [10000, 30000, 60000, 120000]; // 10s, 30s, 60s, 120s
        
        for (const timeout of timeouts) {
            console.log(`\n\n🔄 Testing with ${timeout/1000}s timeout...`);
            
            try {
                const response = await makeRequest('POST', '/api/auth/verify-email', data, {
                    'Content-Type': 'application/json'
                }, timeout);

                console.log(`\n✅ SUCCESS with ${timeout/1000}s timeout!`);
                console.log(`Status: ${response.status}`);
                console.log(`Response Time: ${response.responseTime}ms`);
                
                if (response.status === 200 && response.data && response.data.accessToken) {
                    console.log('✅ Email verification successful!');
                    console.log('Access Token received:', response.data.accessToken ? 'YES' : 'NO');
                    break; // Success, no need to try other timeouts
                } else {
                    console.log('❌ Verification failed but request completed');
                }
                
            } catch (error) {
                console.log(`\n❌ FAILED with ${timeout/1000}s timeout`);
                console.log(`Error: ${error.message}`);
                
                if (timeout === timeouts[timeouts.length - 1]) {
                    console.log('\n❌ All timeout attempts failed');
                }
            }
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error.message);
    } finally {
        rl.close();
    }
}

// Test resend email API
async function testResendEmail() {
    console.log('\n=== TESTING RESEND EMAIL API ===');
    
    const email = await getUserInput('Enter email address for resend: ');
    
    try {
        const response = await makeRequest('POST', '/api/auth/resend-verify-email', { email }, {
            'Content-Type': 'application/json'
        }, 60000);

        console.log(`\n✅ Resend email test completed`);
        console.log(`Status: ${response.status}`);
        console.log(`Response Time: ${response.responseTime}ms`);
        
    } catch (error) {
        console.log(`\n❌ Resend email failed: ${error.message}`);
    }
}

// Main function
async function main() {
    const choice = await getUserInput('Choose test:\n1. Verify Email\n2. Resend Email\n3. Both\nEnter choice (1/2/3): ');
    
    if (choice === '1') {
        await testVerifyEmail();
    } else if (choice === '2') {
        await testResendEmail();
    } else if (choice === '3') {
        await testVerifyEmail();
        await testResendEmail();
    } else {
        console.log('Invalid choice');
        rl.close();
    }
}

// Run the script
if (require.main === module) {
    main();
}
