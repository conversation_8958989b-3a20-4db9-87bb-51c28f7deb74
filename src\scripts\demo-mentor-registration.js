const readline = require('readline');
const https = require('https');
const FormData = require('form-data');

// Configuration
const BASE_URL = 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to make HTTP requests
function makeRequest(method, endpoint, data = null, headers = {}, isFormData = false, timeoutMs = 60000) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);

        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname + url.search,
            method: method,
            timeout: timeoutMs,
            headers: {
                'Accept': 'application/json',
                ...headers
            }
        };

        const req = https.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedData = responseData ? JSON.parse(responseData) : {};
                    resolve({
                        status: res.statusCode,
                        data: parsedData,
                        rawData: responseData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: null,
                        rawData: responseData
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error(`Request timeout after ${timeoutMs}ms`));
        });

        if (data) {
            if (isFormData) {
                data.pipe(req);
            } else {
                req.write(JSON.stringify(data));
            }
        } else {
            req.end();
        }
    });
}

// Get user input
function getUserInput(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Demo function
async function demo() {
    console.log('🚀 DEMO: Mentor Registration API Test');
    console.log('=====================================');
    console.log('');
    console.log('Ví dụ sử dụng:');
    console.log('Email: <EMAIL>');
    console.log('Password: TestPassword123@');
    console.log('Full Name: Test Mentor');
    console.log('');

    try {
        // Get user input
        const email = await getUserInput('Nhập email của bạn: ');
        const password = await getUserInput('Nhập password: ');
        const fullName = await getUserInput('Nhập tên đầy đủ: ');

        console.log('\n=== ĐĂNG KÝ MENTOR ===');
        
        // Create form data for registration
        const formData = new FormData();
        formData.append('Email', email);
        formData.append('Password', password);
        formData.append('FullName', fullName);
        formData.append('Role', '1'); // Mentor role
        formData.append('Bio', 'Test mentor for API testing');
        formData.append('Expertises', '8a5bc300-21c4-47d0-bb33-27d0a709d417');
        formData.append('ProfessionalSkill', 'Software Development');
        formData.append('Experience', '5 years of experience');
        formData.append('CommunicationPreference', '1');
        formData.append('Goals', 'Help others learn programming');
        formData.append('CourseCategoryIds', 'f47ac10b-58cc-4372-a567-0e02b2c3d479');
        formData.append('SessionFrequency', '2');
        formData.append('Duration', '60');
        formData.append('LearningStyle', '1');
        formData.append('TeachingStyles', '1');

        // Send registration request
        const response = await makeRequest('POST', '/api/auth/register', formData, {
            'Accept': '*/*',
            ...formData.getHeaders()
        }, true);

        console.log(`Status: ${response.status}`);
        console.log('Response:', response.rawData);

        if (response.status === 200) {
            console.log('\n✅ Đăng ký thành công! Kiểm tra email để lấy verification code.');
            
            // Get verification code
            const code = await getUserInput('\nNhập verification code từ email: ');
            
            console.log('\n=== XÁC THỰC EMAIL ===');
            
            // Verify email
            const verifyData = {
                email: email,
                code: code
            };

            const verifyResponse = await makeRequest('POST', '/api/auth/verify-email', verifyData, {
                'Content-Type': 'application/json'
            });

            console.log(`Status: ${verifyResponse.status}`);
            console.log('Response:', verifyResponse.rawData);

            if (verifyResponse.status === 200 && verifyResponse.data && verifyResponse.data.accessToken) {
                console.log('\n✅ Xác thực email thành công!');
                console.log('Access Token:', verifyResponse.data.accessToken);
                
                // Test application requests API
                console.log('\n=== TEST APPLICATION REQUESTS API ===');
                
                const appFormData = new FormData();
                appFormData.append('Education', 'Master in Computer Science');
                appFormData.append('WorkExperience', '5 years as Software Developer');
                appFormData.append('Certifications[0]', 'AWS Certified');
                appFormData.append('Certifications[1]', 'Scrum Master');
                appFormData.append('Description', 'Experienced developer passionate about mentoring');

                const appResponse = await makeRequest('POST', '/api/application-requests', appFormData, {
                    'Authorization': `Bearer ${verifyResponse.data.accessToken}`,
                    'Accept': '*/*',
                    ...appFormData.getHeaders()
                }, true);

                console.log(`Application Request Status: ${appResponse.status}`);
                console.log('Application Response:', appResponse.rawData);

                if (appResponse.status === 200) {
                    console.log('\n🎉 TẤT CẢ TEST THÀNH CÔNG!');
                    console.log('✅ Đăng ký mentor: THÀNH CÔNG');
                    console.log('✅ Xác thực email: THÀNH CÔNG');
                    console.log('✅ Tạo application request: THÀNH CÔNG');
                } else {
                    console.log('\n❌ Tạo application request thất bại');
                }
            } else {
                console.log('\n❌ Xác thực email thất bại');
            }
        } else {
            console.log('\n❌ Đăng ký thất bại. Kiểm tra thông tin và thử lại.');
        }

    } catch (error) {
        console.error('❌ Lỗi:', error.message);
    } finally {
        rl.close();
    }
}

// Run demo
if (require.main === module) {
    demo();
}
