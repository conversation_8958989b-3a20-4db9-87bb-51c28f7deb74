# Mentor Application Requests API Testing Script

## <PERSON><PERSON> tả

Script này được tạo để test API mentor application requests với quy trình đăng ký 3 bước:

1. **Đăng ký tài khoản mentor** với email thật từ 10min email
2. **<PERSON><PERSON><PERSON> thực email** với mã code được gửi về email
3. **Test các API endpoints** của application requests

## Cài đặt

Đảm bảo bạn đã cài đặt dependencies:

```bash
npm install
```

## Cách sử dụng

### Bước 1: Chuẩn bị email thật
- Truy cập [10minutemail.com](https://10minutemail.com) để lấy email tạm thời
- Copy email address để sử dụng trong script

### Bước 2: Cấu hình API URL (Tùy chọn)
Script sẽ tự động sử dụng API URL từ file `.env`. Nếu cần thay đổi, cập nhật trong file `.env`:

```bash
API_URL=https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net
```

Hoặc set environment variable:

```bash
export API_URL=https://your-api-domain.com
```

### Bước 3: Test kết nối (Tùy chọn)
Trước khi chạy script chính, bạn có thể test kết nối API:

```bash
npm run test:api:demo
```

### Bước 4: Chạy script chính

```bash
npm run test:api:application-requests
```

Hoặc chạy trực tiếp:

```bash
node scripts/test-application-requests-api.js
```

### Bước 5: Làm theo hướng dẫn

Script sẽ prompt bạn nhập:

1. **Email**: Nhập email thật từ 10min email
2. **Password**: Nhập password cho tài khoản (ví dụ: `123456aA@`)
3. **Verification Code**: Kiểm tra email và nhập mã 6 số được gửi về

## Các API được test

Script sẽ test các endpoints sau:

### 1. Authentication APIs
- `POST /api/auth/register` - Đăng ký tài khoản mentor
- `POST /api/auth/verify-email` - Xác thực email với code
- `POST /api/auth/login` - Đăng nhập (backup nếu verify không trả token)

### 2. Application Requests APIs
- `POST /api/application-requests` - Tạo application request mới
- `GET /api/application-requests/current-user` - Lấy application request của user hiện tại
- `PUT /api/application-requests` - Cập nhật application request
- `GET /api/application-requests/{id}` - Lấy chi tiết application request theo ID

## Dữ liệu test

Dữ liệu test được lưu trong file `src/tests-data/application-requests-api-data.json`:

- **mentorRegistration**: Thông tin đăng ký mentor
- **createRequest**: Dữ liệu tạo application request
- **updateRequest**: Dữ liệu cập nhật application request

## Kết quả mong đợi

Script sẽ hiển thị:

- ✅ Thành công: Khi API trả về status 200 và dữ liệu hợp lệ
- ❌ Thất bại: Khi API trả về lỗi hoặc status khác 200
- 📋 Response details: Chi tiết response từ mỗi API call

## Troubleshooting

### Lỗi thường gặp:

1. **Registration failed**: 
   - Kiểm tra email đã được sử dụng chưa
   - Kiểm tra password có đủ mạnh không

2. **Verification failed**:
   - Kiểm tra mã code có đúng 6 số không
   - Kiểm tra email có nhận được code không
   - Code có thể hết hạn, thử đăng ký lại

3. **API calls failed**:
   - Kiểm tra BASE_URL có đúng không
   - Kiểm tra access token có hợp lệ không
   - Kiểm tra network connection

## Lưu ý

- Sử dụng email thật từ 10min email để nhận verification code
- Mỗi lần chạy script sẽ tạo tài khoản mới
- Script sẽ tự động cleanup browser sau khi hoàn thành
- Tất cả API responses sẽ được log ra console để debug
