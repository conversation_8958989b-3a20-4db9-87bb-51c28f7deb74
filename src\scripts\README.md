# Mentor Registration API Test Script

## <PERSON><PERSON> tả

Script này test quy trình đăng ký mentor với 3 bước:

1. **Đăng ký tài khoản mentor** - G<PERSON>i thông tin đăng ký qua API `/api/auth/register`
2. **<PERSON><PERSON><PERSON> thực email** - Nhập verification code từ email và gửi qua API `/api/auth/verify-email`
3. **Test Application Requests API** - Tạo và lấy application request qua API `/api/application-requests`

## Yêu cầu

- Node.js (version 14 trở lên)
- Email thật (khuyến nghị dùng 10minutemail.com hoặc tương tự)
- Kết nối internet

## Cài đặt

1. Cài đặt dependencies:
```bash
npm install
```

## Cách sử dụng

### Chạy script đầy đủ:

```bash
npm run test:mentor-registration
```

hoặc

```bash
node src/scripts/mentor-registration-test.js
```

### Chạy script demo đơn giản:

```bash
npm run demo:mentor-registration
```

hoặc

```bash
node src/scripts/demo-mentor-registration.js
```

### Quy trình thực hiện:

1. **Nhập thông tin đăng ký:**
   - Email: Nhập email thật (ví dụ từ 10minutemail.com)
   - Password: Nhập mật khẩu mạnh (ít nhất 8 ký tự, có chữ hoa, chữ thường, số và ký tự đặc biệt)
   - Full Name: Nhập tên đầy đủ

2. **Chờ email verification:**
   - Script sẽ gửi request đăng ký
   - Kiểm tra email để lấy verification code
   - Nhập verification code khi được yêu cầu

3. **Test API:**
   - Script sẽ tự động test các API application requests
   - Xem kết quả trong console

## Ví dụ chạy script:

```
🚀 MENTOR REGISTRATION AND APPLICATION REQUESTS API TEST
=====================================================
This script will test the complete mentor registration flow:
1. Register a new mentor account
2. Verify email with code sent to your email
3. Test application requests API

Please use a real email address (e.g., from 10minutemail.com)

Enter your email address: <EMAIL>
Enter your password: TestPassword123@
Enter your full name: Test Mentor

=== STEP 1: REGISTERING MENTOR ACCOUNT ===
Registration Response Status: 200
✅ Registration successful! Check your email for verification code.

Enter the verification code sent to your email: 123456

=== STEP 2: VERIFYING EMAIL ===
Verification Response Status: 200
✅ Email verification successful!

=== STEP 3: TESTING APPLICATION REQUESTS API ===
--- Testing CREATE Application Request ---
Create Application Response Status: 200
✅ Application request created successfully!

--- Testing GET Current User Application Request ---
Get Current Application Response Status: 200
✅ Successfully retrieved current user application request!

🎉 ALL TESTS COMPLETED SUCCESSFULLY!
✅ Mentor registration: SUCCESS
✅ Email verification: SUCCESS
✅ Application requests API: SUCCESS
```

## API Endpoints được test:

1. **POST /api/auth/register** - Đăng ký tài khoản mentor
2. **POST /api/auth/verify-email** - Xác thực email với verification code
3. **POST /api/application-requests** - Tạo application request
4. **GET /api/application-requests/current-user** - Lấy application request của user hiện tại

## Dữ liệu test:

Script sử dụng dữ liệu mẫu cho mentor:
- Role: 1 (Mentor)
- Bio: "Test mentor for API testing"
- Professional Skill: "Software Development"
- Experience: "5 years of experience in software development"
- Communication Preference: 1
- Session Frequency: 2
- Duration: 60 minutes
- Learning Style: 1
- Teaching Styles: 1

Application Request data:
- Education: "Master's in Computer Science from Stanford University"
- Work Experience: "5 years as Senior Software Developer at Google"
- Certifications: ["AWS Certified Solutions Architect", "Scrum Master Certified", "Google Cloud Professional"]
- Description: "Experienced developer passionate about mentoring junior developers"

## Troubleshooting

### Lỗi thường gặp:

1. **Registration failed với status 400:**
   - Email đã được sử dụng
   - Password không đủ mạnh
   - Thiếu thông tin bắt buộc

2. **Email verification failed:**
   - Verification code sai
   - Code đã hết hạn
   - Email không tồn tại

3. **Application requests API failed:**
   - Access token không hợp lệ
   - Thiếu authorization header
   - Dữ liệu request không đúng format

### Debug:

Script sẽ hiển thị chi tiết response từ server để debug. Kiểm tra:
- Status code
- Response body
- Error messages

## Cấu hình

Base URL được cấu hình trong script:
```javascript
const BASE_URL = 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';
```

Có thể thay đổi nếu cần test với environment khác.
