// Demo script to test basic functionality
const { chromium } = require('playwright');
require('dotenv').config();

const BASE_URL = process.env.API_URL || 'https://mentorplatformgr2-be-gwa0fhb2aud4gjew.southeastasia-01.azurewebsites.net';

async function testConnection() {
    console.log('🔗 Testing API connection...');
    console.log('Base URL:', BASE_URL);
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    
    try {
        // Test basic connectivity
        const response = await context.request.get(BASE_URL + '/api/application-requests', {
            headers: {
                'Accept': '*/*'
            }
        });
        
        console.log(`Response status: ${response.status()}`);
        console.log('✅ API endpoint is reachable');
        
        if (response.status() === 401) {
            console.log('🔐 Authentication required (expected for protected endpoints)');
        }
        
    } catch (error) {
        console.error('❌ Connection error:', error.message);
    } finally {
        await browser.close();
    }
}

if (require.main === module) {
    testConnection().catch(console.error);
}
