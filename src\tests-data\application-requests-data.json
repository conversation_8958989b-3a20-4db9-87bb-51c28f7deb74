{"createRequest": {"validData": {"Education": "Master's in Computer Science from Stanford University", "WorkExperience": "5 years as Senior Software Developer at Google, leading teams of 8+ developers", "Certifications": ["AWS Certified Solutions Architect", "Scrum Master Certified", "Google Cloud Professional"], "Description": "Experienced developer passionate about mentoring junior developers and sharing knowledge in full-stack development"}, "invalidData": [{"testCase": "Empty Education", "data": {"Education": "", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "Test description"}, "expectedStatus": 400, "expectedMessage": "Education is required"}, {"testCase": "Empty Work Experience", "data": {"Education": "Bachelor's in Computer Science", "WorkExperience": "", "Certifications": ["AWS"], "Description": "Test description"}, "expectedStatus": 400, "expectedMessage": "Work experience is required"}, {"testCase": "Empty Description", "data": {"Education": "Bachelor's in Computer Science", "WorkExperience": "3 years experience", "Certifications": ["AWS"], "Description": ""}, "expectedStatus": 400, "expectedMessage": "Description is required"}]}, "updateRequest": {"validData": {"Id": "123e4567-e89b-12d3-a456-426614174000", "Education": "PhD in Computer Science from MIT", "WorkExperience": "8 years as Principal Engineer at Microsoft, specializing in cloud architecture", "Certifications": ["Azure Solutions Architect Expert", "Kubernetes Certified Administrator"], "Description": "Updated: Senior engineer with extensive experience in cloud technologies and team leadership"}, "invalidData": [{"testCase": "Application not found", "data": {"Id": "00000000-0000-0000-0000-000000000000", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description"}, "expectedStatus": 404, "expectedMessage": "Application request not found"}, {"testCase": "Cannot update non-review status", "data": {"Id": "123e4567-e89b-12d3-a456-426614174001", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description"}, "expectedStatus": 400, "expectedMessage": "<PERSON><PERSON> cannot update request that is not under review"}]}, "getRequests": {"validParams": [{"testCase": "Basic pagination", "params": {"PageSize": 10, "PageNumber": 1}}, {"testCase": "With search filter", "params": {"PageSize": 5, "PageNumber": 1, "Search": "developer"}}, {"testCase": "With status filter", "params": {"PageSize": 10, "PageNumber": 1, "ApplicationRequestStatuses": [1, 2]}}, {"testCase": "All filters combined", "params": {"PageSize": 20, "PageNumber": 2, "Search": "mentor", "ApplicationRequestStatuses": [0, 1, 2, 3]}}]}, "requestUpdate": {"validData": {"note": "Please provide more details about your teaching experience and add relevant certifications"}, "invalidData": [{"testCase": "Application not found", "id": "00000000-0000-0000-0000-000000000000", "data": {"note": "Test note"}, "expectedStatus": 404, "expectedMessage": "Application request not found"}, {"testCase": "Cannot request update for under review", "id": "123e4567-e89b-12d3-a456-426614174002", "data": {"note": "Test note"}, "expectedStatus": 400, "expectedMessage": "Admin cannot request update request that is under review"}]}, "approveRequest": {"validId": "123e4567-e89b-12d3-a456-426614174000", "invalidData": [{"testCase": "Application not found", "id": "00000000-0000-0000-0000-000000000000", "expectedStatus": 404, "expectedMessage": "Application request not found"}, {"testCase": "Cannot approve under review", "id": "123e4567-e89b-12d3-a456-426614174003", "expectedStatus": 400, "expectedMessage": "Admin cannot approve request that is under review"}]}, "rejectRequest": {"validData": {"note": "Application does not meet our minimum requirements for mentoring experience"}, "invalidData": [{"testCase": "Application not found", "id": "00000000-0000-0000-0000-000000000000", "data": {"note": "Rejection reason"}, "expectedStatus": 404, "expectedMessage": "Application request not found"}, {"testCase": "Cannot reject under review", "id": "123e4567-e89b-12d3-a456-426614174004", "data": {"note": "Rejection reason"}, "expectedStatus": 400, "expectedMessage": "Admin cannot reject request that is under review"}, {"testCase": "Cannot reject approved request", "id": "123e4567-e89b-12d3-a456-426614174005", "data": {"note": "Rejection reason"}, "expectedStatus": 400, "expectedMessage": "Cannot reject approved request"}]}, "testIds": {"validId": "123e4567-e89b-12d3-a456-426614174000", "invalidId": "00000000-0000-0000-0000-000000000000", "underReviewId": "123e4567-e89b-12d3-a456-426614174001", "approvedId": "123e4567-e89b-12d3-a456-426614174005"}, "expectedResponses": {"createSuccess": "Create successfully", "updateSuccess": "Update successfully", "approveSuccess": "Application approved successfully", "rejectSuccess": "Application rejected successfully", "requestUpdateSuccess": "Update request sent successfully"}}